# 🎬 YouTube Shorts Auto Uploader

Fully automated system for uploading YouTube Shorts with optimal timing, engagement features, and duplicate prevention.

## ✨ Features

- **🎯 Smart Video Selection**: Randomly picks videos from all downloaded channels
- **⏰ Optimal Timing**: Uploads 2 videos daily at peak Western audience times
- **🚫 Duplicate Prevention**: Tracks uploaded videos to avoid re-uploads
- **🎨 Engagement Optimization**: Enhances titles, descriptions, and tags
- **📊 Analytics Dashboard**: Monitor upload statistics and system status
- **🔄 Automated Scheduling**: Set-and-forget operation with error handling
- **🛡️ Safety Features**: Dry run mode, error recovery, and logging

## 🚀 Quick Start

### 1. Installation
```bash
python install.py
```

### 2. Setup Google Cloud Console
Follow the detailed guide in `setup_instructions.md`

### 3. Configure
Edit `config.py` with your preferences:
- Upload times and timezone
- Channel branding
- Privacy settings

### 4. Authenticate
```bash
python authenticate.py
# OR
python main.py auth
```

### 5. Test
```bash
python main.py test
```

### 6. Start Automated Uploads
```bash
python main.py run
```

## 📋 Commands

| Command | Description |
|---------|-------------|
| `python main.py status` | Show dashboard and statistics |
| `python main.py upload` | Upload one video immediately |
| `python main.py test` | Test upload (dry run) |
| `python main.py run` | Start automated scheduler |
| `python main.py interactive` | Interactive command mode |

## 📁 Project Structure

```
Youtube Shorts Automation/
├── main.py                 # Main control script
├── scheduler.py            # Automated scheduling system
├── youtube_uploader.py     # YouTube API integration
├── video_selector.py       # Random video selection
├── config.py              # Configuration settings
├── install.py             # Installation script
├── setup_instructions.md  # Detailed setup guide
├── requirements.txt       # Python dependencies
├── upload_registry.json   # Upload tracking database
├── downloads/             # Your downloaded videos
│   ├── Channel1/
│   │   ├── metadata.json
│   │   └── *.mp4
│   └── Channel2/
│       ├── metadata.json
│       └── *.mp4
└── logs/                  # System logs
```

## ⚙️ Configuration

### Upload Schedule
```python
UPLOAD_TIMES = [
    time(9, 0),   # 9:00 AM EST (Morning)
    time(19, 0),  # 7:00 PM EST (Evening)
]
TIMEZONE = "US/Eastern"
```

### Engagement Features
```python
ENGAGEMENT_FEATURES = {
    "add_emoji_to_title": True,
    "enhance_description": True,
    "optimize_tags": True,
    "add_trending_hashtags": True,
    "randomize_upload_time": True,
}
```

### Channel Branding
```python
CHANNEL_WATERMARK = "Your Channel Name"
CHANNEL_HASHTAGS = ["#Shorts", "#Viral", "#Trending"]
```

## 🎯 Optimal Upload Times

The system is pre-configured for Western audiences:
- **Morning**: 9:00 AM EST (when people check phones)
- **Evening**: 7:00 PM EST (prime engagement time)

Random delays (5-30 minutes) are added to avoid predictable patterns.

## 📊 Dashboard

```
🎬 YOUTUBE SHORTS AUTO UPLOADER DASHBOARD
============================================================
System Status: ✅ Ready

📊 UPLOAD STATISTICS
Total Uploaded: 45
Available Videos: 234
Upload History: 45 records

📁 AVAILABLE BY CHANNEL
  CuteBabyCats267: 89 videos
  Pawspace: 78 videos
  Sonyakisa8: 67 videos

📅 TODAY'S STATUS
Uploads Today: 1/2

⏰ UPLOAD SCHEDULE (US/Eastern)
  09:00
  19:00
```

## 🛡️ Safety Features

### Duplicate Prevention
- Tracks all uploaded videos in `upload_registry.json`
- Prevents accidental re-uploads
- Maintains upload history

### Error Handling
- Automatic retry on API errors
- Continues operation on single video failures
- Comprehensive logging

### Testing
- Dry run mode for safe testing
- Authentication verification
- System health checks

## 🖥️ Server Deployment

### Recommended: Small Computer Instance
- **Raspberry Pi 4**: Perfect for 24/7 operation
- **Mini PC**: Intel NUC or similar
- **Cloud VPS**: $5/month DigitalOcean droplet

### Setup for 24/7 Operation
```bash
# Using systemd (Linux)
sudo systemctl enable youtube-uploader
sudo systemctl start youtube-uploader

# Using screen (Simple)
screen -S uploader python3 main.py run
```

## 📈 Engagement Optimization

### Title Enhancement
- Adds engaging emojis
- Optimizes for YouTube algorithm
- Maintains character limits

### Description Optimization
- Includes call-to-action
- Adds channel branding
- Incorporates trending hashtags

### Tag Optimization
- Uses original video tags
- Adds trending keywords
- Limits to optimal count (15)

## 🔧 Troubleshooting

### Common Issues

1. **No Videos Found**
   ```bash
   python video_selector.py  # Test video detection
   ```

2. **Authentication Error**
   ```bash
   python main.py auth  # Re-authenticate
   ```

3. **Upload Failures**
   - Check YouTube API quotas
   - Verify internet connection
   - Review logs: `tail -f uploader.log`

### Debug Mode
```bash
python main.py --debug status
```

## 📝 Requirements

- Python 3.8+
- Google Cloud Console account
- YouTube channel
- Downloaded videos with metadata

## 🔐 Security

- OAuth2 authentication
- Secure credential storage
- No hardcoded secrets
- Local token management

## 📊 Analytics & Monitoring

### Built-in Monitoring
- Upload success/failure tracking
- Daily upload counters
- Video availability statistics
- System health checks

### Log Analysis
```bash
# View recent activity
tail -f uploader.log

# Check for errors
grep ERROR uploader.log

# Monitor uploads
grep "Successfully uploaded" uploader.log
```

## 🎯 Best Practices

### For Maximum Engagement
1. Upload consistently at optimal times
2. Use engaging titles and descriptions
3. Include trending hashtags
4. Monitor analytics and adjust

### For System Reliability
1. Use stable internet connection
2. Monitor disk space
3. Backup upload registry regularly
4. Keep system updated

## 📞 Support

1. **Setup Issues**: Check `setup_instructions.md`
2. **API Problems**: Review Google Cloud Console
3. **System Errors**: Check `uploader.log`
4. **Video Issues**: Test with `video_selector.py`

## 📄 License

This project is for educational and personal use. Ensure compliance with:
- YouTube Terms of Service
- Google API Terms
- Copyright laws
- Platform guidelines

## 🎉 Success Tips

1. **Content Quality**: Ensure videos are engaging and original
2. **Consistency**: Maintain regular upload schedule
3. **Optimization**: Monitor performance and adjust settings
4. **Compliance**: Follow all platform guidelines
5. **Backup**: Regularly backup your registry and settings

---

**Ready to automate your YouTube Shorts success? Start with `python install.py`!** 🚀
