#!/usr/bin/env python3
"""
Server Startup Script for YouTube Shorts Auto Uploader
Handles proper logging and encoding for server deployment
"""

import os
import sys
import logging
import signal
from datetime import datetime

# Set UTF-8 encoding for the entire process
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# Import our modules
import config
from main import YouTubeShortsBot

class ServerManager:
    def __init__(self):
        self.bot = None
        self.setup_logging()
        self.setup_signal_handlers()
    
    def setup_logging(self):
        """Setup server-friendly logging"""
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
        
        # Setup logging with UTF-8 encoding
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # File handler with UTF-8 encoding
        file_handler = logging.FileHandler(
            f'logs/uploader_{datetime.now().strftime("%Y%m%d")}.log',
            encoding='utf-8'
        )
        file_handler.setFormatter(logging.Formatter(log_format))
        
        # Console handler with UTF-8 encoding
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter(log_format))
        
        # Root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config.LOG_LEVEL))
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        # Suppress noisy loggers
        logging.getLogger('googleapiclient.discovery_cache').setLevel(logging.WARNING)
        logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Server logging initialized")
    
    def setup_signal_handlers(self):
        """Setup graceful shutdown handlers"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, shutting down gracefully...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def check_requirements(self):
        """Check if all requirements are met"""
        issues = []
        
        # Check for client secrets
        if not os.path.exists(config.CLIENT_SECRETS_FILE):
            issues.append(f"Missing {config.CLIENT_SECRETS_FILE}")
        
        # Check for token
        if not os.path.exists("token.json"):
            issues.append("Missing token.json - run authentication first")
        
        # Check downloads folder
        if not os.path.exists(config.DOWNLOADS_FOLDER):
            issues.append(f"Missing {config.DOWNLOADS_FOLDER} folder")
        
        # Check for videos
        try:
            from video_selector import VideoSelector
            selector = VideoSelector()
            stats = selector.get_upload_stats()
            if stats['total_available'] == 0:
                issues.append("No videos available for upload")
            else:
                self.logger.info(f"Found {stats['total_available']} videos ready for upload")
        except Exception as e:
            issues.append(f"Error checking videos: {e}")
        
        if issues:
            self.logger.error("Setup issues found:")
            for issue in issues:
                self.logger.error(f"  - {issue}")
            return False
        
        self.logger.info("All requirements met, ready to start")
        return True
    
    def start_server(self):
        """Start the server"""
        self.logger.info("="*60)
        self.logger.info("🎬 YOUTUBE SHORTS AUTO UPLOADER - SERVER MODE")
        self.logger.info("="*60)
        self.logger.info(f"Channel: {config.CHANNEL_WATERMARK}")
        self.logger.info(f"Upload times: {[t.strftime('%H:%M') for t in config.UPLOAD_TIMES]} {config.TIMEZONE}")
        self.logger.info(f"Privacy: {config.DEFAULT_PRIVACY_STATUS}")
        self.logger.info(f"Uploads per day: {config.UPLOADS_PER_DAY}")
        
        # Check requirements
        if not self.check_requirements():
            self.logger.error("Cannot start server due to missing requirements")
            return False
        
        # Create bot instance
        self.bot = YouTubeShortsBot()
        
        # Start the scheduler
        try:
            self.logger.info("Starting automated scheduler...")
            self.bot.scheduler.run_scheduler()
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        except Exception as e:
            self.logger.error(f"Server error: {e}")
            return False
        
        return True
    
    def shutdown(self):
        """Graceful shutdown"""
        self.logger.info("Shutting down server...")
        if self.bot:
            # Add any cleanup here if needed
            pass
        self.logger.info("Server shutdown complete")

def main():
    """Main entry point"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "check":
            # Just check requirements and exit
            manager = ServerManager()
            success = manager.check_requirements()
            sys.exit(0 if success else 1)
        elif command == "test":
            # Run a test upload
            manager = ServerManager()
            if manager.check_requirements():
                bot = YouTubeShortsBot()
                success = bot.test_upload()
                print("✅ Test successful!" if success else "❌ Test failed!")
            sys.exit(0)
        elif command == "upload":
            # Upload one video
            manager = ServerManager()
            if manager.check_requirements():
                bot = YouTubeShortsBot()
                success = bot.manual_upload()
                print("✅ Upload successful!" if success else "❌ Upload failed!")
            sys.exit(0)
    
    # Default: start server
    manager = ServerManager()
    success = manager.start_server()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
