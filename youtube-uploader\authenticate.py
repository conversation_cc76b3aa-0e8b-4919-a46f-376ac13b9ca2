#!/usr/bin/env python3
"""
Simple Authentication Script for YouTube Shorts Auto Uploader
Run this first to authenticate with YouTube API
"""

import os
import json
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# YouTube API configuration
SCOPES = ["https://www.googleapis.com/auth/youtube.upload"]
CLIENT_SECRETS_FILE = "client_secrets.json"
TOKEN_FILE = "token.json"

def authenticate():
    """Authenticate with YouTube API and save token"""
    print("🔐 YouTube API Authentication")
    print("="*50)
    
    # Check if client secrets file exists
    if not os.path.exists(CLIENT_SECRETS_FILE):
        print(f"❌ Error: {CLIENT_SECRETS_FILE} not found!")
        print("Please download this file from Google Cloud Console:")
        print("1. Go to Google Cloud Console")
        print("2. APIs & Services > Credentials")
        print("3. Download your OAuth 2.0 Client ID JSON")
        print("4. Rename it to 'client_secrets.json'")
        return False
    
    creds = None
    
    # Load existing token if available
    if os.path.exists(TOKEN_FILE):
        print(f"📄 Found existing {TOKEN_FILE}")
        try:
            creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
            print("✅ Loaded existing credentials")
        except Exception as e:
            print(f"⚠️  Error loading existing token: {e}")
            print("Will create new authentication...")
    
    # If no valid credentials, get new ones
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            print("🔄 Refreshing expired token...")
            try:
                creds.refresh(Request())
                print("✅ Token refreshed successfully")
            except Exception as e:
                print(f"❌ Failed to refresh token: {e}")
                print("Will create new authentication...")
                creds = None
        
        if not creds:
            print("🌐 Starting new authentication flow...")
            print("This will open a browser window for you to login to Google")
            print("Please:")
            print("1. Login to your Google account")
            print("2. Grant permissions to the app")
            print("3. Return here when done")
            
            try:
                flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRETS_FILE, SCOPES)
                creds = flow.run_local_server(port=0)
                print("✅ Authentication successful!")
            except Exception as e:
                print(f"❌ Authentication failed: {e}")
                return False
        
        # Save credentials for next run
        try:
            with open(TOKEN_FILE, 'w') as token:
                token.write(creds.to_json())
            print(f"💾 Saved credentials to {TOKEN_FILE}")
        except Exception as e:
            print(f"❌ Failed to save token: {e}")
            return False
    
    # Test the credentials by building YouTube service
    try:
        print("🧪 Testing YouTube API connection...")
        service = build("youtube", "v3", credentials=creds)
        
        # Try to get channel info to verify access
        request = service.channels().list(part="snippet", mine=True)
        response = request.execute()
        
        if response.get("items"):
            channel = response["items"][0]["snippet"]
            print(f"✅ Successfully connected to YouTube!")
            print(f"📺 Channel: {channel.get('title', 'Unknown')}")
            print(f"📊 Channel ID: {response['items'][0]['id']}")
        else:
            print("⚠️  Connected but no channel found")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to connect to YouTube API: {e}")
        return False

def main():
    """Main function"""
    print("🎬 YouTube Shorts Auto Uploader - Authentication")
    print("This will authenticate your app with YouTube API")
    print()
    
    success = authenticate()
    
    if success:
        print("\n🎉 AUTHENTICATION COMPLETE!")
        print("You can now use the uploader:")
        print("  python main.py test     - Test upload")
        print("  python main.py upload   - Upload one video")
        print("  python main.py run      - Start automated uploads")
        print()
        print("💡 Your token.json file has been created.")
        print("💡 Copy this file to your server for remote uploads.")
    else:
        print("\n❌ AUTHENTICATION FAILED!")
        print("Please check:")
        print("1. client_secrets.json file exists and is valid")
        print("2. YouTube Data API v3 is enabled in Google Cloud Console")
        print("3. Your Google account has access to the YouTube channel")

if __name__ == "__main__":
    main()
