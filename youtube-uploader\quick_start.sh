#!/bin/bash
# Quick Start Script for YouTube Shorts Auto Uploader

echo "🎬 YouTube Shorts Auto Uploader - Quick Start"
echo "============================================="

# Check if we're on the server
if [ -f "/etc/os-release" ]; then
    . /etc/os-release
    if [[ "$ID" == "ubuntu" ]]; then
        echo "✅ Detected Ubuntu server"
        SERVER_MODE=true
    fi
fi

# Function to check file exists
check_file() {
    if [ -f "$1" ]; then
        echo "✅ $1 found"
        return 0
    else
        echo "❌ $1 missing"
        return 1
    fi
}

# Check required files
echo ""
echo "📋 Checking required files..."
check_file "client_secrets.json"
CLIENT_SECRETS=$?

check_file "token.json"
TOKEN=$?

check_file "requirements.txt"
REQUIREMENTS=$?

# Check downloads folder
if [ -d "downloads" ]; then
    VIDEO_COUNT=$(find downloads/ -name "*.mp4" 2>/dev/null | wc -l)
    echo "✅ downloads folder found ($VIDEO_COUNT videos)"
    DOWNLOADS=0
else
    echo "❌ downloads folder missing"
    DOWNLOADS=1
fi

# Install dependencies if needed
if [ $REQUIREMENTS -eq 0 ]; then
    echo ""
    echo "📦 Installing dependencies..."
    pip3 install -r requirements.txt
fi

# Check system
echo ""
echo "🧪 Testing system..."
python3 start_server.py check

# Show next steps based on what's missing
echo ""
echo "📋 STATUS SUMMARY:"

if [ $CLIENT_SECRETS -ne 0 ]; then
    echo "❌ Missing client_secrets.json"
    echo "   Download from Google Cloud Console"
fi

if [ $TOKEN -ne 0 ]; then
    echo "❌ Missing token.json"
    echo "   Run: python3 authenticate.py"
fi

if [ $DOWNLOADS -ne 0 ]; then
    echo "❌ Missing video files"
    echo "   Upload your downloads/ folder"
fi

# If everything is ready
if [ $CLIENT_SECRETS -eq 0 ] && [ $TOKEN -eq 0 ] && [ $DOWNLOADS -eq 0 ]; then
    echo "✅ All files present!"
    echo ""
    echo "🚀 READY TO START!"
    echo ""
    echo "Choose an option:"
    echo "1. Test upload (dry run)"
    echo "2. Upload one video"
    echo "3. Start 24/7 automation"
    echo "4. Setup as system service"
    echo ""
    read -p "Enter choice (1-4): " choice
    
    case $choice in
        1)
            echo "🧪 Running test upload..."
            python3 start_server.py test
            ;;
        2)
            echo "🚀 Uploading one video..."
            python3 start_server.py upload
            ;;
        3)
            echo "🔄 Starting 24/7 automation..."
            if [ "$SERVER_MODE" = true ]; then
                echo "Use Ctrl+C to stop"
                python3 start_server.py
            else
                echo "Starting in screen session..."
                screen -S uploader python3 start_server.py
            fi
            ;;
        4)
            if [ "$SERVER_MODE" = true ]; then
                echo "🔧 Setting up system service..."
                ./setup_server.sh
            else
                echo "❌ System service setup only available on Linux servers"
            fi
            ;;
        *)
            echo "Invalid choice"
            ;;
    esac
else
    echo ""
    echo "⚠️  Please fix the missing files above, then run this script again"
fi

echo ""
echo "💡 Manual commands:"
echo "   python3 authenticate.py        - Authenticate with YouTube"
echo "   python3 start_server.py test   - Test upload"
echo "   python3 start_server.py upload - Upload one video"
echo "   python3 start_server.py        - Start automation"
echo "   python3 main.py status         - Check status"
