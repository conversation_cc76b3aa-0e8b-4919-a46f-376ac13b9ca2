#!/bin/bash
# Server Setup Script for YouTube Shorts Auto Uploader

set -e  # Exit on any error

echo "🎬 YouTube Shorts Auto Uploader - Server Setup"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "Don't run this script as root!"
    exit 1
fi

# Update system
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
print_status "Installing Python and dependencies..."
sudo apt install -y python3 python3-pip python3-venv git screen htop nano curl

# Install Python packages
print_status "Installing Python packages..."
pip3 install -r requirements.txt

# Create logs directory
print_status "Creating logs directory..."
mkdir -p logs

# Check for required files
print_status "Checking required files..."

if [ ! -f "client_secrets.json" ]; then
    print_warning "client_secrets.json not found!"
    echo "Please upload this file from Google Cloud Console"
fi

if [ ! -f "token.json" ]; then
    print_warning "token.json not found!"
    echo "Please copy this file from your local authentication"
fi

if [ ! -d "downloads" ]; then
    print_warning "downloads folder not found!"
    echo "Please upload your video files"
fi

# Test the system
print_status "Testing system..."
python3 start_server.py check

# Setup systemd service
print_status "Setting up systemd service..."

# Update service file with correct user and path
USER=$(whoami)
CURRENT_DIR=$(pwd)

# Create updated service file
cat > youtube-uploader-temp.service << EOF
[Unit]
Description=YouTube Shorts Auto Uploader
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=$USER
Group=$USER
WorkingDirectory=$CURRENT_DIR
Environment=PYTHONUNBUFFERED=1
Environment=PYTHONIOENCODING=utf-8
ExecStart=/usr/bin/python3 start_server.py
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$CURRENT_DIR

[Install]
WantedBy=multi-user.target
EOF

# Install service
sudo cp youtube-uploader-temp.service /etc/systemd/system/youtube-uploader.service
rm youtube-uploader-temp.service

# Reload systemd
sudo systemctl daemon-reload

print_status "Setup complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Upload missing files (client_secrets.json, token.json, downloads/)"
echo "2. Test: python3 start_server.py test"
echo "3. Start service: sudo systemctl start youtube-uploader"
echo "4. Enable auto-start: sudo systemctl enable youtube-uploader"
echo "5. Check status: sudo systemctl status youtube-uploader"
echo "6. View logs: sudo journalctl -u youtube-uploader -f"
echo ""
echo "🎯 Manual Commands:"
echo "  python3 start_server.py check   - Check requirements"
echo "  python3 start_server.py test    - Test upload"
echo "  python3 start_server.py upload  - Upload one video"
echo "  python3 start_server.py         - Start server"
echo ""
echo "🔧 Service Commands:"
echo "  sudo systemctl start youtube-uploader    - Start service"
echo "  sudo systemctl stop youtube-uploader     - Stop service"
echo "  sudo systemctl restart youtube-uploader  - Restart service"
echo "  sudo systemctl status youtube-uploader   - Check status"
echo "  sudo journalctl -u youtube-uploader -f   - View live logs"
