#!/usr/bin/env python3
"""
System Test Script for YouTube Shorts Auto Uploader
Tests all components without uploading
"""

import os
import sys
import json
import logging
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import config
        print("✅ config.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import config: {e}")
        return False
    
    try:
        from video_selector import VideoSelector
        print("✅ video_selector.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import video_selector: {e}")
        return False
    
    try:
        from youtube_uploader import YouTubeUploader
        print("✅ youtube_uploader.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import youtube_uploader: {e}")
        return False
    
    try:
        from scheduler import UploadScheduler
        print("✅ scheduler.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import scheduler: {e}")
        return False
    
    return True

def test_video_selector():
    """Test video selection functionality"""
    print("\n🎯 Testing video selector...")
    
    try:
        from video_selector import VideoSelector
        selector = VideoSelector()
        
        # Test getting channels
        channels = selector.get_all_channels()
        print(f"✅ Found {len(channels)} channels: {channels}")
        
        # Test getting statistics
        stats = selector.get_upload_stats()
        print(f"✅ Statistics: {stats['total_available']} available videos")
        
        # Test selecting a video (without marking as uploaded)
        if stats['total_available'] > 0:
            video = selector.select_random_video()
            if video:
                print(f"✅ Selected video: {video['filename']} from {video['channel_name']}")
            else:
                print("⚠️  No video selected (this might be normal)")
        else:
            print("⚠️  No videos available for selection")
        
        return True
        
    except Exception as e:
        print(f"❌ Video selector test failed: {e}")
        return False

def test_configuration():
    """Test configuration settings"""
    print("\n⚙️ Testing configuration...")
    
    try:
        import config
        
        # Check required settings
        required_settings = [
            'YOUTUBE_API_SCOPES',
            'CLIENT_SECRETS_FILE',
            'UPLOADS_PER_DAY',
            'UPLOAD_TIMES',
            'DOWNLOADS_FOLDER',
            'REGISTRY_FILE'
        ]
        
        for setting in required_settings:
            if hasattr(config, setting):
                value = getattr(config, setting)
                print(f"✅ {setting}: {value}")
            else:
                print(f"❌ Missing setting: {setting}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_file_structure():
    """Test file structure and permissions"""
    print("\n📁 Testing file structure...")
    
    # Check required files
    required_files = [
        'config.py',
        'video_selector.py',
        'youtube_uploader.py',
        'scheduler.py',
        'main.py',
        'requirements.txt'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
            return False
    
    # Check downloads folder
    if os.path.exists('downloads'):
        print("✅ downloads folder exists")
        
        # Check for channel folders
        channel_folders = [d for d in Path('downloads').iterdir() if d.is_dir()]
        if channel_folders:
            print(f"✅ Found {len(channel_folders)} channel folders")
            
            # Check for metadata files
            metadata_count = 0
            video_count = 0
            
            for folder in channel_folders:
                metadata_file = folder / 'metadata.json'
                if metadata_file.exists():
                    metadata_count += 1
                    
                    # Count videos
                    videos = list(folder.glob('*.mp4'))
                    video_count += len(videos)
            
            print(f"✅ Found {metadata_count} metadata files")
            print(f"✅ Found {video_count} video files")
            
        else:
            print("⚠️  No channel folders found in downloads/")
    else:
        print("❌ downloads folder missing")
        return False
    
    # Check registry file
    if os.path.exists('upload_registry.json'):
        print("✅ upload_registry.json exists")
        try:
            with open('upload_registry.json', 'r') as f:
                registry = json.load(f)
            print(f"✅ Registry contains {len(registry.get('uploaded_videos', []))} uploaded videos")
        except Exception as e:
            print(f"⚠️  Registry file exists but has issues: {e}")
    else:
        print("⚠️  upload_registry.json missing (will be created)")
    
    return True

def test_youtube_api_setup():
    """Test YouTube API setup (without authentication)"""
    print("\n🔐 Testing YouTube API setup...")
    
    # Check for client secrets file
    if os.path.exists('client_secrets.json'):
        print("✅ client_secrets.json found")
        
        try:
            with open('client_secrets.json', 'r') as f:
                secrets = json.load(f)
            
            if 'installed' in secrets:
                client_info = secrets['installed']
                if 'client_id' in client_info and 'client_secret' in client_info:
                    print("✅ Client secrets file appears valid")
                else:
                    print("❌ Client secrets file missing required fields")
                    return False
            else:
                print("❌ Client secrets file has wrong format")
                return False
                
        except Exception as e:
            print(f"❌ Error reading client secrets: {e}")
            return False
    else:
        print("❌ client_secrets.json not found")
        print("   Download this from Google Cloud Console")
        return False
    
    # Check for existing token
    if os.path.exists('token.json'):
        print("✅ token.json found (already authenticated)")
    else:
        print("⚠️  token.json not found (authentication needed)")
    
    return True

def test_dry_run():
    """Test dry run functionality"""
    print("\n🧪 Testing dry run mode...")
    
    try:
        # Set dry run mode
        import config
        original_dry_run = config.DRY_RUN
        config.DRY_RUN = True
        
        from youtube_uploader import YouTubeUploader
        uploader = YouTubeUploader()
        
        # Test video selection
        from video_selector import VideoSelector
        selector = VideoSelector()
        video = selector.select_random_video()
        
        if video:
            print(f"✅ Would upload: {video['filename']}")
            
            # Test upload preparation (without actual upload)
            title = uploader.enhance_title(video.get('title', 'Test Title'))
            description = uploader.enhance_description(video)
            tags = uploader.optimize_tags(video)
            
            print(f"✅ Enhanced title: {title[:50]}...")
            print(f"✅ Enhanced description: {len(description)} characters")
            print(f"✅ Optimized tags: {len(tags)} tags")
            
        else:
            print("⚠️  No video available for dry run test")
        
        # Restore original setting
        config.DRY_RUN = original_dry_run
        
        return True
        
    except Exception as e:
        print(f"❌ Dry run test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🎬 YOUTUBE SHORTS AUTO UPLOADER - SYSTEM TEST")
    print("="*60)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("File Structure", test_file_structure),
        ("Video Selector", test_video_selector),
        ("YouTube API Setup", test_youtube_api_setup),
        ("Dry Run", test_dry_run)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("Your system is ready for YouTube Shorts automation!")
        print("\nNext steps:")
        print("1. Run: python main.py test")
        print("2. Run: python main.py upload")
        print("3. Run: python main.py run")
    else:
        print("\n⚠️  SOME TESTS FAILED")
        print("Please address the issues above before proceeding.")
        print("Check setup_instructions.md for help.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
