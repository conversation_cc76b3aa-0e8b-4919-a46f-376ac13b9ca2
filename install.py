#!/usr/bin/env python3
"""
Installation and Setup Script for YouTube Shorts Auto Uploader
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_header():
    print("="*60)
    print("🎬 YOUTUBE SHORTS AUTO UPLOADER - INSTALLATION")
    print("="*60)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """Install required Python packages"""
    print("\n📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = ["downloads", "logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created/verified directory: {directory}")

def check_downloads_folder():
    """Check if downloads folder has content"""
    downloads_path = Path("downloads")
    if not downloads_path.exists():
        print("❌ Downloads folder not found!")
        return False
    
    # Check for channel folders
    channel_folders = [d for d in downloads_path.iterdir() if d.is_dir()]
    if not channel_folders:
        print("⚠️  No channel folders found in downloads/")
        print("   Please run your video downloader first!")
        return False
    
    # Check for metadata files
    metadata_files = 0
    video_files = 0
    
    for channel_folder in channel_folders:
        metadata_file = channel_folder / "metadata.json"
        if metadata_file.exists():
            metadata_files += 1
            # Count video files
            video_files += len(list(channel_folder.glob("*.mp4")))
    
    print(f"✅ Found {len(channel_folders)} channel folders")
    print(f"✅ Found {metadata_files} metadata files")
    print(f"✅ Found {video_files} video files")
    
    return metadata_files > 0 and video_files > 0

def create_sample_client_secrets():
    """Create a sample client_secrets.json file"""
    sample_secrets = {
        "installed": *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
    
    with open("client_secrets_sample.json", "w") as f:
        json.dump(sample_secrets, f, indent=2)
    
    print("✅ Created client_secrets_sample.json")

def check_client_secrets():
    """Check for client secrets file"""
    if os.path.exists("client_secrets.json"):
        print("✅ client_secrets.json found")
        return True
    else:
        print("❌ client_secrets.json not found")
        print("   Download this file from Google Cloud Console")
        create_sample_client_secrets()
        return False

def run_system_check():
    """Run a comprehensive system check"""
    print("\n🔍 Running system check...")
    
    # Import our modules to test them
    try:
        import config
        print("✅ Configuration module loaded")
    except ImportError as e:
        print(f"❌ Failed to import config: {e}")
        return False
    
    try:
        from video_selector import VideoSelector
        selector = VideoSelector()
        stats = selector.get_upload_stats()
        print(f"✅ Video selector working - {stats['total_available']} videos available")
    except Exception as e:
        print(f"❌ Video selector error: {e}")
        return False
    
    return True

def show_next_steps():
    """Show what to do next"""
    print("\n🎯 NEXT STEPS:")
    print("1. 📋 Follow setup_instructions.md for Google Cloud Console setup")
    print("2. 📥 Download client_secrets.json from Google Cloud Console")
    print("3. ⚙️  Customize config.py with your preferences")
    print("4. 🧪 Test the system: python main.py test")
    print("5. 🚀 Start uploading: python main.py run")
    
    print("\n💡 QUICK START:")
    print("   python main.py status    - Check system status")
    print("   python main.py test      - Test upload (dry run)")
    print("   python main.py upload    - Upload one video")
    print("   python main.py run       - Start automated scheduler")

def main():
    print_header()
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Create directories
    create_directories()
    
    # Check downloads folder
    downloads_ok = check_downloads_folder()
    
    # Check client secrets
    secrets_ok = check_client_secrets()
    
    # Run system check
    system_ok = run_system_check()
    
    # Summary
    print("\n" + "="*60)
    print("📋 INSTALLATION SUMMARY")
    print("="*60)
    
    print(f"Python Version: {'✅' if True else '❌'}")
    print(f"Dependencies: {'✅' if True else '❌'}")
    print(f"Downloads Folder: {'✅' if downloads_ok else '⚠️'}")
    print(f"Client Secrets: {'✅' if secrets_ok else '❌'}")
    print(f"System Check: {'✅' if system_ok else '❌'}")
    
    if downloads_ok and secrets_ok and system_ok:
        print("\n🎉 INSTALLATION COMPLETE!")
        print("Your YouTube Shorts Auto Uploader is ready to use!")
    else:
        print("\n⚠️  INSTALLATION INCOMPLETE")
        print("Please address the issues above before proceeding.")
    
    show_next_steps()
    
    return downloads_ok and secrets_ok and system_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
