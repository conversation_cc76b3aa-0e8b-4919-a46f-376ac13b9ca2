# YouTube Shorts Auto Uploader Setup Instructions

## Overview
This system automatically uploads 2 YouTube Shorts per day at optimal times for Western audiences, randomly selecting from your downloaded videos while avoiding duplicates.

## Prerequisites
- Python 3.8 or higher
- Google Cloud Console account
- YouTube channel for uploading

## Step 1: Install Dependencies

```bash
pip install -r requirements.txt
```

## Step 2: Google Cloud Console Setup

### 2.1 Create a Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "New Project" or select existing project
3. Name your project (e.g., "YouTube Auto Uploader")
4. Note your Project ID

### 2.2 Enable YouTube Data API v3
1. In Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "YouTube Data API v3"
3. Click on it and press "Enable"

### 2.3 Create OAuth 2.0 Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. If prompted, configure OAuth consent screen:
   - Choose "External" user type
   - Fill in app name, user support email, developer email
   - Add your email to test users
   - Add scopes: `../auth/youtube.upload`
4. For OAuth client ID:
   - Application type: "Desktop application"
   - Name: "YouTube Auto Uploader"
5. Download the JSON file and rename it to `client_secrets.json`
6. Place `client_secrets.json` in your project root directory

### 2.4 OAuth Consent Screen (Important!)
1. Go to "APIs & Services" > "OAuth consent screen"
2. Add your YouTube channel email to "Test users"
3. For production use, submit for verification (optional for personal use)

## Step 3: Configuration

### 3.1 Edit config.py
Customize these settings in `config.py`:

```python
# Upload times (adjust for your timezone)
UPLOAD_TIMES = [
    time(9, 0),   # 9:00 AM EST
    time(19, 0),  # 7:00 PM EST
]

# Your timezone
TIMEZONE = "US/Eastern"  # Change to your timezone

# Channel branding
CHANNEL_WATERMARK = "Your Channel Name"
CHANNEL_HASHTAGS = ["#Shorts", "#YourChannel", "#Trending"]

# Privacy settings
DEFAULT_PRIVACY_STATUS = "public"  # or "private", "unlisted"
```

### 3.2 Test Configuration
```bash
python scheduler.py test
```

## Step 4: First Run Authentication

### 4.1 Authenticate
```bash
python scheduler.py upload
```

This will:
1. Open a browser window
2. Ask you to sign in to Google
3. Grant permissions to your app
4. Save authentication token for future use

### 4.2 Verify Setup
```bash
python scheduler.py status
```

## Step 5: Running the System

### 5.1 Manual Upload (Testing)
```bash
python scheduler.py upload
```

### 5.2 Start Automated Scheduler
```bash
python scheduler.py run
```

### 5.3 Check Status
```bash
python scheduler.py status
```

## Step 6: Server Deployment

### 6.1 For Small Computer Instance (Recommended)

#### Option A: Raspberry Pi / Mini PC
1. Install Python 3.8+
2. Clone your project
3. Install dependencies
4. Copy `client_secrets.json` and `token.json`
5. Run: `python scheduler.py run`

#### Option B: Cloud VPS (DigitalOcean, Linode, etc.)
1. Create Ubuntu 20.04+ droplet ($5/month minimum)
2. Install Python: `sudo apt update && sudo apt install python3 python3-pip`
3. Upload your project files
4. Install dependencies: `pip3 install -r requirements.txt`
5. Run with screen: `screen -S uploader python3 scheduler.py run`

### 6.2 Keep Running 24/7

#### Using systemd (Linux)
Create `/etc/systemd/system/youtube-uploader.service`:

```ini
[Unit]
Description=YouTube Shorts Auto Uploader
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/your/project
ExecStart=/usr/bin/python3 scheduler.py run
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable youtube-uploader
sudo systemctl start youtube-uploader
sudo systemctl status youtube-uploader
```

#### Using screen (Simple)
```bash
screen -S uploader
python3 scheduler.py run
# Press Ctrl+A, then D to detach
# Use 'screen -r uploader' to reattach
```

## Step 7: Monitoring and Maintenance

### 7.1 Check Logs
```bash
tail -f uploader.log
```

### 7.2 Monitor Upload Status
```bash
python scheduler.py status
```

### 7.3 Add More Videos
- Download new videos to your channels folders
- The system will automatically detect them

### 7.4 Backup Registry
```bash
cp upload_registry.json upload_registry_backup.json
```

## Troubleshooting

### Common Issues

1. **Authentication Error**
   - Delete `token.json` and re-authenticate
   - Check `client_secrets.json` is valid

2. **No Videos Found**
   - Verify video files exist in downloads folders
   - Check metadata.json files are present

3. **Upload Quota Exceeded**
   - YouTube has daily upload limits
   - Wait 24 hours or reduce upload frequency

4. **API Errors**
   - Check YouTube Data API is enabled
   - Verify OAuth credentials are correct

### Getting Help

1. Check logs in `uploader.log`
2. Run with debug: Set `LOG_LEVEL = "DEBUG"` in config.py
3. Test individual components:
   ```bash
   python video_selector.py
   python youtube_uploader.py
   ```

## Security Notes

- Keep `client_secrets.json` and `token.json` secure
- Don't commit these files to version control
- Use environment variables for sensitive data in production
- Regularly backup your upload registry

## Performance Optimization

### For Better Engagement:
1. Enable all engagement features in config.py
2. Customize title prefixes and hashtags
3. Adjust upload times for your audience
4. Monitor analytics and adjust strategy

### For System Performance:
1. Use SSD storage for faster video access
2. Ensure stable internet connection
3. Monitor system resources
4. Set up log rotation

## Legal Considerations

- Ensure you have rights to upload all videos
- Respect YouTube's Terms of Service
- Don't upload copyrighted content without permission
- Follow community guidelines

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review YouTube API documentation
3. Check Google Cloud Console for API limits
4. Monitor system logs for detailed error messages
