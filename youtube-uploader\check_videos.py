#!/usr/bin/env python3
"""
Check Video Status Script
"""

import os
import json
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
import config

def check_videos():
    """Check status of uploaded videos"""
    
    # Load credentials
    if not os.path.exists("token.json"):
        print("❌ No token.json found")
        return False
    
    creds = Credentials.from_authorized_user_file("token.json", config.YOUTUBE_API_SCOPES)
    service = build("youtube", "v3", credentials=creds)
    
    # Get recent uploads
    try:
        # Get channel uploads playlist
        channels_response = service.channels().list(
            mine=True,
            part='contentDetails'
        ).execute()
        
        if not channels_response['items']:
            print("❌ No channel found")
            return False
        
        uploads_playlist_id = channels_response['items'][0]['contentDetails']['relatedPlaylists']['uploads']
        
        # Get recent videos from uploads playlist
        playlist_response = service.playlistItems().list(
            playlistId=uploads_playlist_id,
            part='snippet',
            maxResults=10
        ).execute()
        
        print("📺 Recent Videos:")
        print("="*50)
        
        for item in playlist_response['items']:
            video_id = item['snippet']['resourceId']['videoId']
            title = item['snippet']['title']
            
            # Get detailed video info
            video_response = service.videos().list(
                id=video_id,
                part='snippet,status,processingDetails'
            ).execute()
            
            if video_response['items']:
                video = video_response['items'][0]
                status = video['status']
                
                print(f"\n🎬 Title: {title}")
                print(f"🆔 ID: {video_id}")
                print(f"🔗 URL: https://www.youtube.com/watch?v={video_id}")
                print(f"🔒 Privacy: {status.get('privacyStatus', 'unknown')}")
                print(f"👶 Made for Kids: {status.get('madeForKids', 'not set')}")
                print(f"📤 Upload Status: {status.get('uploadStatus', 'unknown')}")
                print(f"🎯 Publish At: {status.get('publishAt', 'not set')}")
                
                if 'processingDetails' in video:
                    processing = video['processingDetails']
                    print(f"⚙️  Processing Status: {processing.get('processingStatus', 'unknown')}")
                
                # Check if video needs completion
                if status.get('uploadStatus') == 'uploaded' and 'madeForKids' not in status:
                    print("⚠️  VIDEO NEEDS 'MADE FOR KIDS' SETTING!")
                
                print("-" * 30)
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking videos: {e}")
        return False

if __name__ == "__main__":
    print("📺 YouTube Video Status Checker")
    print("="*50)
    check_videos()
