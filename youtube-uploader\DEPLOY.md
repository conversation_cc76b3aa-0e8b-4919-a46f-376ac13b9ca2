# 🚀 Server Deployment Guide

## Quick Deploy to Google Cloud VM

### 1. Create VM Instance
```bash
# Create e2-micro instance (FREE TIER)
gcloud compute instances create youtube-uploader \
    --zone=us-central1-a \
    --machine-type=e2-micro \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --boot-disk-size=10GB
```

### 2. Connect to VM
```bash
# SSH into VM
gcloud compute ssh youtube-uploader --zone=us-central1-a
```

### 3. Clone and Setup
```bash
# Clone repository
git clone <your-repo-url>
cd youtube-uploader

# Run setup script
chmod +x setup_server.sh
./setup_server.sh
```

### 4. Upload Required Files
```bash
# From your local computer, upload files:
gcloud compute scp client_secrets.json youtube-uploader:~/youtube-uploader/ --zone=us-central1-a
gcloud compute scp token.json youtube-uploader:~/youtube-uploader/ --zone=us-central1-a

# Upload video folders (if needed)
gcloud compute scp --recurse downloads/ youtube-uploader:~/youtube-uploader/ --zone=us-central1-a
```

### 5. Test and Start
```bash
# Test the system
python3 start_server.py test

# Start the service
sudo systemctl start youtube-uploader
sudo systemctl enable youtube-uploader

# Check status
sudo systemctl status youtube-uploader
```

## Alternative: Manual Setup

### 1. Basic Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3 python3-pip git screen

# Clone repo
git clone <your-repo-url>
cd youtube-uploader

# Install Python packages
pip3 install -r requirements.txt
```

### 2. Upload Files
```bash
# Upload via SCP
scp client_secrets.json user@server-ip:~/youtube-uploader/
scp token.json user@server-ip:~/youtube-uploader/
scp -r downloads/ user@server-ip:~/youtube-uploader/
```

### 3. Test System
```bash
# Check requirements
python3 start_server.py check

# Test upload
python3 start_server.py test

# Upload one video
python3 start_server.py upload
```

### 4. Start 24/7 Service

#### Option A: Using Systemd (Recommended)
```bash
# Setup service
sudo cp youtube-uploader.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable youtube-uploader
sudo systemctl start youtube-uploader

# Monitor
sudo systemctl status youtube-uploader
sudo journalctl -u youtube-uploader -f
```

#### Option B: Using Screen (Simple)
```bash
# Start screen session
screen -S uploader

# Run the bot
python3 start_server.py

# Detach: Ctrl+A, then D
# Reattach: screen -r uploader
```

## Monitoring Commands

### Service Status
```bash
# Check if running
sudo systemctl status youtube-uploader

# View logs
sudo journalctl -u youtube-uploader -f

# Restart service
sudo systemctl restart youtube-uploader
```

### Manual Monitoring
```bash
# Check system
python3 start_server.py check

# View recent logs
tail -f logs/uploader_$(date +%Y%m%d).log

# Check upload status
python3 main.py status
```

## Troubleshooting

### Service Won't Start
```bash
# Check logs
sudo journalctl -u youtube-uploader --no-pager

# Check file permissions
ls -la client_secrets.json token.json

# Test manually
python3 start_server.py check
```

### Upload Failures
```bash
# Check authentication
python3 authenticate.py

# Test upload
python3 start_server.py test

# Check video files
ls -la downloads/*/
```

### No Videos Found
```bash
# Check downloads folder
find downloads/ -name "*.mp4" | wc -l

# Check metadata files
find downloads/ -name "metadata.json"

# Test video selector
python3 video_selector.py
```

## Performance Optimization

### For Better Performance
```bash
# Monitor system resources
htop

# Check disk space
df -h

# Monitor network
iftop
```

### Log Rotation
```bash
# Setup logrotate
sudo nano /etc/logrotate.d/youtube-uploader

# Add this content:
/home/<USER>/youtube-uploader/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 ubuntu ubuntu
}
```

## Security

### Firewall (Optional)
```bash
# Basic firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow out 443  # HTTPS for YouTube API
```

### File Permissions
```bash
# Secure sensitive files
chmod 600 client_secrets.json token.json
chmod 755 *.py
```

## Cost Optimization

### Free Tier Limits
- **e2-micro**: 1 instance free per month
- **Storage**: 30GB free
- **Network**: 1GB egress free per month

### Stop/Start VM
```bash
# Stop VM (stops billing)
gcloud compute instances stop youtube-uploader --zone=us-central1-a

# Start VM
gcloud compute instances start youtube-uploader --zone=us-central1-a
```

## Success Checklist

- ✅ VM created and accessible
- ✅ Dependencies installed
- ✅ Files uploaded (client_secrets.json, token.json)
- ✅ Videos available in downloads/
- ✅ Test upload successful
- ✅ Service running and enabled
- ✅ Logs showing successful uploads
- ✅ YouTube Studio showing uploaded videos

## Support

### Get Help
```bash
# System info
python3 start_server.py check

# Recent logs
tail -50 logs/uploader_$(date +%Y%m%d).log

# Service status
sudo systemctl status youtube-uploader --no-pager
```

Your YouTube Shorts Auto Uploader should now be running 24/7! 🎉
