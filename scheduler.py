"""
Scheduler Module for YouTube Shorts Auto Uploader
Handles automated daily uploads at optimal times
"""

import schedule
import time
import logging
import random
from datetime import datetime, timedelta
try:
    import pytz
except ImportError:
    print("Installing pytz...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pytz"])
    import pytz
from typing import List

import config
from youtube_uploader import YouTubeUploader

class UploadScheduler:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.uploader = YouTubeUploader()
        self.timezone = pytz.timezone(config.TIMEZONE)
        self.daily_upload_count = 0
        self.last_upload_date = None
        
    def setup_logging(self):
        """Setup logging configuration"""
        if config.ENABLE_LOGGING:
            logging.basicConfig(
                level=getattr(logging, config.LOG_LEVEL),
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(config.LOG_FILE),
                    logging.StreamHandler()
                ]
            )
    
    def authenticate_uploader(self) -> bool:
        """Authenticate the YouTube uploader"""
        return self.uploader.authenticate()
    
    def reset_daily_counter(self):
        """Reset daily upload counter if it's a new day"""
        current_date = datetime.now(self.timezone).date()
        if self.last_upload_date != current_date:
            self.daily_upload_count = 0
            self.last_upload_date = current_date
            self.logger.info(f"Reset daily counter for {current_date}")
    
    def can_upload_today(self) -> bool:
        """Check if we can still upload today"""
        self.reset_daily_counter()
        return self.daily_upload_count < config.UPLOADS_PER_DAY
    
    def add_random_delay(self):
        """Add random delay to avoid predictable upload patterns"""
        if config.ENGAGEMENT_FEATURES.get("randomize_upload_time", False):
            delay = random.randint(
                config.MIN_DELAY_BETWEEN_UPLOADS,
                config.MAX_DELAY_BETWEEN_UPLOADS
            )
            self.logger.info(f"Adding random delay of {delay} seconds")
            time.sleep(delay)
    
    def scheduled_upload(self):
        """Perform a scheduled upload"""
        try:
            self.logger.info("Starting scheduled upload...")
            
            # Check if we can upload today
            if not self.can_upload_today():
                self.logger.info(f"Daily upload limit reached ({config.UPLOADS_PER_DAY})")
                return
            
            # Add random delay for engagement optimization
            self.add_random_delay()
            
            # Perform upload
            success = self.uploader.upload_random_video()
            
            if success:
                self.daily_upload_count += 1
                self.logger.info(f"Upload successful! Daily count: {self.daily_upload_count}/{config.UPLOADS_PER_DAY}")
                
                # Log statistics
                stats = self.uploader.video_selector.get_upload_stats()
                self.logger.info(f"Remaining videos: {stats['total_available']}")
                
            else:
                self.logger.error("Upload failed!")
                
                if not config.CONTINUE_ON_ERROR:
                    self.logger.error("Stopping scheduler due to upload failure")
                    return
                    
        except Exception as e:
            self.logger.error(f"Error during scheduled upload: {e}")
            
            if not config.CONTINUE_ON_ERROR:
                self.logger.error("Stopping scheduler due to error")
                return
    
    def setup_schedule(self):
        """Setup the upload schedule"""
        self.logger.info("Setting up upload schedule...")
        
        # Clear any existing schedule
        schedule.clear()
        
        # Schedule uploads at configured times
        for upload_time in config.UPLOAD_TIMES:
            time_str = upload_time.strftime("%H:%M")
            schedule.every().day.at(time_str).do(self.scheduled_upload)
            self.logger.info(f"Scheduled daily upload at {time_str} {config.TIMEZONE}")
        
        # Log next scheduled uploads
        self.log_next_uploads()
    
    def log_next_uploads(self):
        """Log information about next scheduled uploads"""
        jobs = schedule.get_jobs()
        if jobs:
            self.logger.info("Next scheduled uploads:")
            for job in jobs:
                next_run = job.next_run
                if next_run:
                    local_time = next_run.astimezone(self.timezone)
                    self.logger.info(f"  - {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    
    def run_scheduler(self):
        """Run the scheduler continuously"""
        self.logger.info("Starting YouTube Shorts Auto Uploader Scheduler...")
        
        # Setup logging
        self.setup_logging()
        
        # Authenticate
        if not self.authenticate_uploader():
            self.logger.error("Failed to authenticate with YouTube API")
            return
        
        # Setup schedule
        self.setup_schedule()
        
        # Log initial statistics
        stats = self.uploader.video_selector.get_upload_stats()
        self.logger.info(f"Initial statistics:")
        self.logger.info(f"  Total uploaded: {stats['total_uploaded']}")
        self.logger.info(f"  Total available: {stats['total_available']}")
        self.logger.info(f"  Available by channel: {stats['available_by_channel']}")
        
        # Main scheduler loop
        self.logger.info("Scheduler is running... Press Ctrl+C to stop")
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            self.logger.info("Scheduler stopped by user")
        except Exception as e:
            self.logger.error(f"Scheduler error: {e}")
    
    def run_single_upload(self):
        """Run a single upload immediately (for testing)"""
        self.logger.info("Running single upload...")
        
        # Setup logging
        self.setup_logging()
        
        # Authenticate
        if not self.authenticate_uploader():
            self.logger.error("Failed to authenticate with YouTube API")
            return False
        
        # Perform upload
        return self.uploader.upload_random_video()
    
    def show_status(self):
        """Show current status and statistics"""
        print("\n=== YouTube Shorts Auto Uploader Status ===")
        
        # Upload statistics
        stats = self.uploader.video_selector.get_upload_stats()
        print(f"Total uploaded: {stats['total_uploaded']}")
        print(f"Total available: {stats['total_available']}")
        print(f"Upload history: {stats['upload_history_count']} records")
        
        print(f"\nAvailable videos by channel:")
        for channel, count in stats['available_by_channel'].items():
            print(f"  {channel}: {count} videos")
        
        # Daily status
        self.reset_daily_counter()
        print(f"\nDaily uploads: {self.daily_upload_count}/{config.UPLOADS_PER_DAY}")
        
        # Next scheduled uploads
        print(f"\nScheduled upload times ({config.TIMEZONE}):")
        for upload_time in config.UPLOAD_TIMES:
            print(f"  {upload_time.strftime('%H:%M')}")
        
        # Configuration
        print(f"\nConfiguration:")
        print(f"  Privacy: {config.DEFAULT_PRIVACY_STATUS}")
        print(f"  Dry run: {config.DRY_RUN}")
        print(f"  Continue on error: {config.CONTINUE_ON_ERROR}")
        print(f"  Randomize timing: {config.ENGAGEMENT_FEATURES.get('randomize_upload_time', False)}")


def main():
    """Main function with command line interface"""
    import sys
    
    scheduler = UploadScheduler()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "run":
            scheduler.run_scheduler()
        elif command == "upload":
            success = scheduler.run_single_upload()
            print("Upload successful!" if success else "Upload failed!")
        elif command == "status":
            scheduler.show_status()
        elif command == "test":
            # Test mode - set dry run and upload once
            import config
            config.DRY_RUN = True
            success = scheduler.run_single_upload()
            print("Test upload successful!" if success else "Test upload failed!")
        else:
            print("Usage: python scheduler.py [run|upload|status|test]")
            print("  run    - Start the automated scheduler")
            print("  upload - Upload one video immediately")
            print("  status - Show current status and statistics")
            print("  test   - Test upload (dry run)")
    else:
        print("YouTube Shorts Auto Uploader Scheduler")
        print("Usage: python scheduler.py [run|upload|status|test]")
        scheduler.show_status()


if __name__ == "__main__":
    main()
