#!/usr/bin/env python3
"""
YouTube Shorts Auto Uploader - Main Control Script
Automated system for uploading YouTube Shorts with optimal timing and engagement features
"""

import os
import sys
import logging
import argparse
from datetime import datetime
import json

# Import our modules
from scheduler import UploadScheduler
from video_selector import VideoSelector
from youtube_uploader import YouTubeUploader
import config

class YouTubeShortsBot:
    def __init__(self):
        self.scheduler = UploadScheduler()
        self.video_selector = VideoSelector()
        self.uploader = YouTubeUploader()
        self.setup_logging()
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        if config.ENABLE_LOGGING:
            logging.basicConfig(
                level=getattr(logging, config.LOG_LEVEL),
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(config.LOG_FILE),
                    logging.StreamHandler()
                ]
            )
        self.logger = logging.getLogger(__name__)
    
    def check_setup(self) -> bool:
        """Check if the system is properly set up"""
        issues = []
        
        # Check for client secrets
        if not os.path.exists(config.CLIENT_SECRETS_FILE):
            issues.append(f"Missing {config.CLIENT_SECRETS_FILE} - Download from Google Cloud Console")
        
        # Check downloads folder
        if not os.path.exists(config.DOWNLOADS_FOLDER):
            issues.append(f"Missing {config.DOWNLOADS_FOLDER} folder")
        
        # Check for videos
        stats = self.video_selector.get_upload_stats()
        if stats['total_available'] == 0:
            issues.append("No videos available for upload")
        
        # Check registry
        if not os.path.exists(config.REGISTRY_FILE):
            self.logger.info(f"Creating new registry file: {config.REGISTRY_FILE}")
            self.video_selector.save_registry({"uploaded_videos": [], "upload_history": []})
        
        if issues:
            print("❌ Setup Issues Found:")
            for issue in issues:
                print(f"  - {issue}")
            print("\n📖 Please check setup_instructions.md for help")
            return False
        
        print("✅ System setup looks good!")
        return True
    
    def show_dashboard(self):
        """Show comprehensive dashboard"""
        print("\n" + "="*60)
        print("🎬 YOUTUBE SHORTS AUTO UPLOADER DASHBOARD")
        print("="*60)
        
        # System status
        setup_ok = self.check_setup()
        print(f"System Status: {'✅ Ready' if setup_ok else '❌ Needs Setup'}")
        
        # Upload statistics
        stats = self.video_selector.get_upload_stats()
        print(f"\n📊 UPLOAD STATISTICS")
        print(f"Total Uploaded: {stats['total_uploaded']}")
        print(f"Available Videos: {stats['total_available']}")
        print(f"Upload History: {stats['upload_history_count']} records")
        
        # Channel breakdown
        if stats['available_by_channel']:
            print(f"\n📁 AVAILABLE BY CHANNEL")
            for channel, count in stats['available_by_channel'].items():
                print(f"  {channel}: {count} videos")
        
        # Daily status
        self.scheduler.reset_daily_counter()
        print(f"\n📅 TODAY'S STATUS")
        print(f"Uploads Today: {self.scheduler.daily_upload_count}/{config.UPLOADS_PER_DAY}")
        
        # Schedule
        print(f"\n⏰ UPLOAD SCHEDULE ({config.TIMEZONE})")
        for upload_time in config.UPLOAD_TIMES:
            print(f"  {upload_time.strftime('%H:%M')}")
        
        # Configuration
        print(f"\n⚙️ CONFIGURATION")
        print(f"Privacy: {config.DEFAULT_PRIVACY_STATUS}")
        print(f"Dry Run: {'Yes' if config.DRY_RUN else 'No'}")
        print(f"Continue on Error: {'Yes' if config.CONTINUE_ON_ERROR else 'No'}")
        print(f"Random Timing: {'Yes' if config.ENGAGEMENT_FEATURES.get('randomize_upload_time') else 'No'}")
        
        # Recent uploads
        registry = self.video_selector.load_registry()
        recent_uploads = registry.get("upload_history", [])[-5:]  # Last 5
        if recent_uploads:
            print(f"\n📤 RECENT UPLOADS")
            for upload in reversed(recent_uploads):
                date = upload.get('upload_date', 'Unknown')[:10] if upload.get('upload_date') else 'Unknown'
                title = upload.get('filename', 'Unknown')[:40]
                channel = upload.get('channel', 'Unknown')
                print(f"  {date} - {title} ({channel})")
        
        print("="*60)
    
    def run_interactive_mode(self):
        """Run interactive command mode"""
        print("\n🎬 YouTube Shorts Auto Uploader - Interactive Mode")
        print("Type 'help' for available commands, 'quit' to exit")
        
        while True:
            try:
                command = input("\n> ").strip().lower()
                
                if command in ['quit', 'exit', 'q']:
                    print("Goodbye! 👋")
                    break
                elif command in ['help', 'h']:
                    self.show_help()
                elif command in ['status', 's']:
                    self.show_dashboard()
                elif command in ['upload', 'u']:
                    self.manual_upload()
                elif command in ['run', 'start']:
                    print("Starting automated scheduler...")
                    self.scheduler.run_scheduler()
                elif command in ['test', 't']:
                    self.test_upload()
                elif command in ['config', 'c']:
                    self.show_config()
                elif command in ['reset']:
                    self.reset_registry()
                elif command in ['auth']:
                    self.authenticate()
                else:
                    print(f"Unknown command: {command}. Type 'help' for available commands.")
                    
            except KeyboardInterrupt:
                print("\nGoodbye! 👋")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    def show_help(self):
        """Show available commands"""
        print("\n📋 AVAILABLE COMMANDS:")
        print("  status (s)    - Show dashboard and statistics")
        print("  upload (u)    - Upload one video immediately")
        print("  run (start)   - Start automated scheduler")
        print("  test (t)      - Test upload (dry run)")
        print("  config (c)    - Show configuration")
        print("  auth          - Re-authenticate with YouTube")
        print("  reset         - Reset upload registry (careful!)")
        print("  help (h)      - Show this help")
        print("  quit (q)      - Exit program")
    
    def manual_upload(self):
        """Perform manual upload"""
        print("🚀 Starting manual upload...")
        success = self.scheduler.run_single_upload()
        if success:
            print("✅ Upload successful!")
        else:
            print("❌ Upload failed! Check logs for details.")
    
    def test_upload(self):
        """Test upload without actually uploading"""
        print("🧪 Running test upload (dry run)...")
        original_dry_run = config.DRY_RUN
        config.DRY_RUN = True
        
        success = self.scheduler.run_single_upload()
        
        config.DRY_RUN = original_dry_run
        
        if success:
            print("✅ Test successful! System is ready for real uploads.")
        else:
            print("❌ Test failed! Check setup and configuration.")
    
    def show_config(self):
        """Show current configuration"""
        print("\n⚙️ CURRENT CONFIGURATION:")
        print(f"Upload Times: {[t.strftime('%H:%M') for t in config.UPLOAD_TIMES]}")
        print(f"Timezone: {config.TIMEZONE}")
        print(f"Uploads Per Day: {config.UPLOADS_PER_DAY}")
        print(f"Privacy Status: {config.DEFAULT_PRIVACY_STATUS}")
        print(f"Channel Watermark: {config.CHANNEL_WATERMARK}")
        print(f"Channel Hashtags: {config.CHANNEL_HASHTAGS}")
        print(f"Dry Run: {config.DRY_RUN}")
        print(f"Continue on Error: {config.CONTINUE_ON_ERROR}")
        print(f"Log Level: {config.LOG_LEVEL}")
    
    def reset_registry(self):
        """Reset upload registry with confirmation"""
        print("⚠️  WARNING: This will reset the upload registry!")
        print("All upload history will be lost and videos may be re-uploaded.")
        confirm = input("Type 'RESET' to confirm: ")
        
        if confirm == 'RESET':
            self.video_selector.reset_registry()
            print("✅ Registry reset successfully!")
        else:
            print("❌ Reset cancelled.")
    
    def authenticate(self):
        """Re-authenticate with YouTube"""
        print("🔐 Re-authenticating with YouTube...")
        
        # Remove existing token
        if os.path.exists("token.json"):
            os.remove("token.json")
            print("Removed existing authentication token")
        
        success = self.uploader.authenticate()
        if success:
            print("✅ Authentication successful!")
        else:
            print("❌ Authentication failed! Check your client_secrets.json file.")


def main():
    """Main entry point with command line arguments"""
    parser = argparse.ArgumentParser(description="YouTube Shorts Auto Uploader")
    parser.add_argument('command', nargs='?', choices=['run', 'upload', 'status', 'test', 'interactive', 'auth'],
                       help='Command to execute')
    parser.add_argument('--dry-run', action='store_true', help='Test mode without actual uploads')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    
    args = parser.parse_args()
    
    # Set debug mode
    if args.debug:
        config.LOG_LEVEL = "DEBUG"
    
    # Set dry run mode
    if args.dry_run:
        config.DRY_RUN = True
        print("🧪 DRY RUN MODE ENABLED - No actual uploads will be performed")
    
    bot = YouTubeShortsBot()
    
    if args.command == 'run':
        print("🚀 Starting automated scheduler...")
        bot.scheduler.run_scheduler()
    elif args.command == 'upload':
        bot.manual_upload()
    elif args.command == 'status':
        bot.show_dashboard()
    elif args.command == 'test':
        bot.test_upload()
    elif args.command == 'interactive':
        bot.run_interactive_mode()
    elif args.command == 'auth':
        bot.authenticate()
    else:
        # Default: show dashboard and enter interactive mode
        bot.show_dashboard()
        print("\n💡 Tip: Use 'python main.py interactive' for command mode")
        print("💡 Tip: Use 'python main.py run' to start the scheduler")
        print("💡 Tip: Use 'python main.py --help' for all options")


if __name__ == "__main__":
    main()
