"""
Video Selection Module for YouTube Shorts Auto Uploader
Handles random video selection from all downloaded channels
"""

import os
import json
import random
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import config

class VideoSelector:
    def __init__(self):
        self.downloads_folder = config.DOWNLOADS_FOLDER
        self.registry_file = config.REGISTRY_FILE
        self.logger = logging.getLogger(__name__)
        
    def load_registry(self) -> Dict:
        """Load the upload registry to track uploaded videos"""
        if os.path.exists(self.registry_file):
            try:
                with open(self.registry_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Error loading registry: {e}")
                return {"uploaded_videos": [], "upload_history": []}
        return {"uploaded_videos": [], "upload_history": []}
    
    def save_registry(self, registry: Dict):
        """Save the upload registry"""
        try:
            with open(self.registry_file, 'w', encoding='utf-8') as f:
                json.dump(registry, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving registry: {e}")
    
    def get_all_channels(self) -> List[str]:
        """Get list of all channel folders"""
        channels = []
        if os.path.exists(self.downloads_folder):
            for item in os.listdir(self.downloads_folder):
                channel_path = os.path.join(self.downloads_folder, item)
                if os.path.isdir(channel_path) and item != "__pycache__":
                    channels.append(item)
        return channels
    
    def load_channel_metadata(self, channel_name: str) -> Optional[Dict]:
        """Load metadata for a specific channel"""
        metadata_path = os.path.join(self.downloads_folder, channel_name, "metadata.json")
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Error loading metadata for {channel_name}: {e}")
        return None
    
    def get_available_videos(self) -> List[Dict]:
        """Get all available videos from all channels that haven't been uploaded"""
        registry = self.load_registry()
        uploaded_videos = set(registry.get("uploaded_videos", []))
        available_videos = []
        
        channels = self.get_all_channels()
        self.logger.info(f"Found {len(channels)} channels: {channels}")
        
        for channel in channels:
            metadata = self.load_channel_metadata(channel)
            if not metadata:
                continue
                
            channel_path = os.path.join(self.downloads_folder, channel)
            
            for video_info in metadata.get("videos", []):
                filename = video_info.get("filename", "")
                if not filename:
                    continue
                    
                # Create unique identifier for the video
                video_id = f"{channel}_{filename}"
                
                # Skip if already uploaded
                if video_id in uploaded_videos:
                    continue
                
                # Check if video file exists
                video_path = os.path.join(channel_path, f"{filename}.mp4")
                if not os.path.exists(video_path):
                    self.logger.warning(f"Video file not found: {video_path}")
                    continue
                
                # Add channel info and file path to video info
                video_data = video_info.copy()
                video_data.update({
                    "channel_name": channel,
                    "video_id": video_id,
                    "file_path": video_path,
                    "original_channel": metadata.get("channel_name", channel)
                })
                
                available_videos.append(video_data)
        
        self.logger.info(f"Found {len(available_videos)} available videos")
        return available_videos
    
    def select_random_video(self) -> Optional[Dict]:
        """Select a random video that hasn't been uploaded yet"""
        available_videos = self.get_available_videos()
        
        if not available_videos:
            self.logger.warning("No available videos found!")
            return None
        
        # Randomly select a video
        selected_video = random.choice(available_videos)
        self.logger.info(f"Selected video: {selected_video['filename']} from {selected_video['channel_name']}")
        
        return selected_video
    
    def mark_video_as_uploaded(self, video_data: Dict, upload_result: Dict = None):
        """Mark a video as uploaded in the registry"""
        registry = self.load_registry()
        
        # Add to uploaded videos list
        video_id = video_data.get("video_id")
        if video_id and video_id not in registry["uploaded_videos"]:
            registry["uploaded_videos"].append(video_id)
        
        # Add to upload history with details
        upload_record = {
            "video_id": video_id,
            "filename": video_data.get("filename"),
            "channel": video_data.get("channel_name"),
            "title": video_data.get("title"),
            "upload_date": upload_result.get("upload_date") if upload_result else None,
            "youtube_id": upload_result.get("youtube_id") if upload_result else None,
            "status": upload_result.get("status", "uploaded") if upload_result else "uploaded"
        }
        
        registry["upload_history"].append(upload_record)
        
        # Keep only last 1000 records to prevent file from getting too large
        if len(registry["upload_history"]) > 1000:
            registry["upload_history"] = registry["upload_history"][-1000:]
        
        self.save_registry(registry)
        self.logger.info(f"Marked video as uploaded: {video_id}")
    
    def get_upload_stats(self) -> Dict:
        """Get statistics about uploads"""
        registry = self.load_registry()
        available_videos = self.get_available_videos()
        
        total_uploaded = len(registry.get("uploaded_videos", []))
        total_available = len(available_videos)
        
        # Count videos by channel
        channel_stats = {}
        for video in available_videos:
            channel = video.get("channel_name", "unknown")
            channel_stats[channel] = channel_stats.get(channel, 0) + 1
        
        return {
            "total_uploaded": total_uploaded,
            "total_available": total_available,
            "available_by_channel": channel_stats,
            "upload_history_count": len(registry.get("upload_history", []))
        }
    
    def reset_registry(self):
        """Reset the upload registry (use with caution!)"""
        empty_registry = {"uploaded_videos": [], "upload_history": []}
        self.save_registry(empty_registry)
        self.logger.warning("Upload registry has been reset!")


def main():
    """Test the video selector"""
    logging.basicConfig(level=logging.INFO)
    selector = VideoSelector()
    
    # Print statistics
    stats = selector.get_upload_stats()
    print(f"Upload Statistics:")
    print(f"  Total uploaded: {stats['total_uploaded']}")
    print(f"  Total available: {stats['total_available']}")
    print(f"  Available by channel: {stats['available_by_channel']}")
    
    # Select a random video
    video = selector.select_random_video()
    if video:
        print(f"\nSelected video:")
        print(f"  Title: {video['title']}")
        print(f"  Channel: {video['channel_name']}")
        print(f"  File: {video['filename']}")
        print(f"  Path: {video['file_path']}")
    else:
        print("No videos available for upload")


if __name__ == "__main__":
    main()
