#!/usr/bin/env python3
"""
Test Upload Script - Debug YouTube API calls
"""

import os
import json
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import config

def test_upload():
    """Test upload with detailed logging"""
    
    # Load credentials
    if not os.path.exists("token.json"):
        print("❌ No token.json found")
        return False
    
    creds = Credentials.from_authorized_user_file("token.json", config.YOUTUBE_API_SCOPES)
    service = build("youtube", "v3", credentials=creds)
    
    # Find a test video
    from video_selector import VideoSelector
    selector = VideoSelector()
    video_data = selector.select_random_video()
    
    if not video_data:
        print("❌ No video found")
        return False
    
    print(f"📹 Testing with: {video_data['filename']}")
    print(f"📁 File path: {video_data['file_path']}")
    
    # Prepare upload data
    title = f"🐱 TEST - {video_data.get('title', 'Test Video')}"
    description = f"""
🐱 Welcome to Cats FurReal – where paws, purrs, and pure chaos collide! 😹

🔔 Subscribe for daily cat content!
👍 Like if this made you smile!
💬 Tell us about your cat in the comments!

Does your cat do this too? Let us know! 👇

Cats FurReal

#Shorts #CatsFurReal #CuteCats #FunnyCats #CatVideos #Viral
"""
    
    # Test different API body configurations
    test_bodies = [
        {
            "name": "Basic Upload",
            "body": {
                "snippet": {
                    "title": title,
                    "description": description,
                    "tags": ["cats", "funny", "cute", "shorts"],
                    "categoryId": "22"
                },
                "status": {
                    "privacyStatus": "private"
                }
            }
        },
        {
            "name": "With selfDeclaredMadeForKids",
            "body": {
                "snippet": {
                    "title": title,
                    "description": description,
                    "tags": ["cats", "funny", "cute", "shorts"],
                    "categoryId": "22"
                },
                "status": {
                    "privacyStatus": "private",
                    "selfDeclaredMadeForKids": False
                }
            }
        },
        {
            "name": "With madeForKids",
            "body": {
                "snippet": {
                    "title": title,
                    "description": description,
                    "tags": ["cats", "funny", "cute", "shorts"],
                    "categoryId": "22"
                },
                "status": {
                    "privacyStatus": "private",
                    "madeForKids": False
                }
            }
        }
    ]
    
    for test in test_bodies:
        print(f"\n🧪 Testing: {test['name']}")
        print(f"📋 Body: {json.dumps(test['body'], indent=2)}")
        
        try:
            # Create media upload
            media = MediaFileUpload(
                video_data["file_path"],
                chunksize=1024*1024,
                resumable=True
            )
            
            # Execute upload
            insert_request = service.videos().insert(
                part=",".join(test['body'].keys()),
                body=test['body'],
                media_body=media
            )
            
            response = None
            while response is None:
                status, response = insert_request.next_chunk()
                if response is not None:
                    if 'id' in response:
                        print(f"✅ Upload successful!")
                        print(f"📺 Video ID: {response['id']}")
                        print(f"🔗 URL: https://www.youtube.com/watch?v={response['id']}")
                        print(f"📊 Response: {json.dumps(response, indent=2)}")
                        
                        # Check video details
                        video_details = service.videos().list(
                            part="snippet,status",
                            id=response['id']
                        ).execute()
                        
                        print(f"📋 Video Details: {json.dumps(video_details, indent=2)}")
                        return True
                    else:
                        print(f"❌ Upload failed: {response}")
                        return False
                        
        except Exception as e:
            print(f"❌ Error: {e}")
            continue
    
    return False

if __name__ == "__main__":
    print("🧪 YouTube API Upload Test")
    print("="*50)
    success = test_upload()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}")
