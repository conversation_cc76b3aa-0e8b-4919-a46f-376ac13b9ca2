# 🎬 YouTube Shorts Auto Uploader

Automated system for uploading YouTube Shorts with optimal timing and engagement features.

**Configured for: Cats FurReal Channel** 🐱

## ✨ Features

- **🎯 Smart Video Selection**: Randomly picks videos from downloaded channels
- **⏰ Optimal Timing**: 2 uploads daily at 9 AM & 7 PM EST (Western audience)
- **🚫 Duplicate Prevention**: Never uploads the same video twice
- **🎨 Engagement Optimization**: Enhanced titles, descriptions, and tags
- **📊 Dashboard**: Monitor upload statistics and system status
- **🛡️ Safety**: Private uploads for testing, comprehensive logging

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Setup Google Cloud Console
- Create project and enable YouTube Data API v3
- Download `client_secrets.json` and place in this folder
- See `setup_instructions.md` for detailed steps

### 3. Authenticate
```bash
python authenticate.py
```

### 4. Test System
```bash
python main.py test
```

### 5. Upload One Video (Private)
```bash
python main.py upload
```

### 6. Start Automated Uploads
```bash
python main.py run
```

## 📋 Commands

| Command | Description |
|---------|-------------|
| `python main.py auth` | Authenticate with YouTube |
| `python main.py status` | Show dashboard and statistics |
| `python main.py test` | Test upload (dry run) |
| `python main.py upload` | Upload one video immediately |
| `python main.py run` | Start automated scheduler |

## ⚙️ Configuration

### Channel Settings (Already Configured)
```python
CHANNEL_WATERMARK = "Cats FurReal"
CHANNEL_HASHTAGS = ["#Shorts", "#CatsFurReal", "#CuteCats", "#FunnyCats", "#Trending"]
```

### Upload Schedule (Perfect for Western Audience)
```python
UPLOAD_TIMES = [
    time(9, 0),   # 9:00 AM EST
    time(19, 0),  # 7:00 PM EST
]
TIMEZONE = "US/Eastern"
```

### Privacy Settings
```python
DEFAULT_PRIVACY_STATUS = "private"  # Change to "public" when ready
```

## 📁 Required Files

```
youtube-uploader/
├── main.py                 # Main control script
├── authenticate.py         # Authentication helper
├── scheduler.py            # Automated scheduling
├── youtube_uploader.py     # YouTube API integration
├── video_selector.py       # Video selection logic
├── config.py              # Configuration (pre-configured)
├── requirements.txt       # Dependencies
├── client_secrets.json    # Download from Google Cloud Console
├── token.json            # Created after authentication
├── upload_registry.json  # Upload tracking (auto-created)
└── downloads/            # Your video files
    ├── CuteBabyCats267/
    ├── Pawspace/
    ├── Sonyakisa8/
    └── exlittlebeans/
```

## 🖥️ Server Deployment

### Quick Server Setup
```bash
# 1. Clone to server
git clone <your-repo> && cd youtube-uploader

# 2. Run quick setup
chmod +x quick_start.sh
./quick_start.sh

# 3. Upload required files (client_secrets.json, token.json)
# 4. Follow the interactive prompts
```

### Manual Server Setup
```bash
# 1. Setup environment
./setup_server.sh

# 2. Test system
python3 start_server.py test

# 3. Start as service
sudo systemctl start youtube-uploader
sudo systemctl enable youtube-uploader
```

### Monitor Server
```bash
# Check service status
sudo systemctl status youtube-uploader

# View live logs
sudo journalctl -u youtube-uploader -f

# Manual commands
python3 start_server.py check   # Check requirements
python3 start_server.py test    # Test upload
python3 start_server.py upload  # Upload one video
```

## 🎯 Engagement Features

### Optimized for Cat Content
- **Cat-focused emojis**: 😻 🐱 😹 🐾
- **Engaging descriptions**: "Does your cat do this too?"
- **Cat-specific hashtags**: #CatsFurReal #CuteCats #FunnyCats
- **Channel branding**: "Welcome to Cats FurReal"

### Automatic Enhancements
- Adds emojis to titles
- Includes call-to-action in descriptions
- Optimizes tags for discoverability
- Random timing to avoid patterns

## 🛡️ Safety Features

- **Private uploads first**: Test before going public
- **Duplicate prevention**: Tracks all uploaded videos
- **Error handling**: Continues on failures
- **Comprehensive logging**: Monitor all activity
- **Dry run mode**: Test without uploading

## 📊 Expected Results

With 2 daily uploads at optimal times:
- **Consistent Growth**: Regular content builds audience
- **Better Engagement**: Optimized timing and content
- **Viral Potential**: Enhanced titles and descriptions
- **Time Savings**: Fully automated operation

## 🔧 Troubleshooting

### Authentication Issues
```bash
# Re-authenticate
python authenticate.py

# Check token
python main.py auth
```

### No Videos Found
```bash
# Test video detection
python video_selector.py

# Check downloads folder structure
```

### Upload Failures
```bash
# Check logs
tail -f uploader.log

# Test with dry run
python main.py test
```

## 💡 Tips

1. **Start with private uploads** to test everything
2. **Monitor logs regularly** for any issues
3. **Change to public** when ready: Edit `config.py`
4. **Backup registry** to prevent re-uploads
5. **Check YouTube Studio** to see uploaded videos

## 🎉 Ready to Go!

Your YouTube Shorts Auto Uploader is configured for "Cats FurReal" and ready to automatically grow your channel! 🚀

**Next Steps:**
1. `python authenticate.py` - Login to YouTube
2. `python main.py test` - Test the system
3. `python main.py run` - Start automated uploads

Perfect for 24/7 operation on any server! 🐱
