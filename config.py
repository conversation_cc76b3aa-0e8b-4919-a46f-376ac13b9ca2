"""
Configuration settings for YouTube Shorts Auto Uploader
"""

import os
from datetime import time

# YouTube API Configuration
YOUTUBE_API_SCOPES = ["https://www.googleapis.com/auth/youtube.upload"]
YOUTUBE_API_SERVICE_NAME = "youtube"
YOUTUBE_API_VERSION = "v3"

# OAuth2 credentials file (download from Google Cloud Console)
CLIENT_SECRETS_FILE = "client_secrets.json"

# Upload Configuration
UPLOADS_PER_DAY = 2
UPLOAD_TIMES = [
    time(9, 0),   # 9:00 AM EST (Morning upload)
    time(19, 0),  # 7:00 PM EST (Evening upload)
]

# Video Selection Configuration
DOWNLOADS_FOLDER = "downloads"
REGISTRY_FILE = "upload_registry.json"
LOG_FILE = "uploader.log"

# Video Upload Settings
DEFAULT_PRIVACY_STATUS = "private"  # Start with "private" for testing, change to "public" later
DEFAULT_CATEGORY_ID = "22"  # People & Blogs category
DEFAULT_TAGS_LIMIT = 15  # Maximum number of tags to use

# Channel Branding
CHANNEL_WATERMARK = "Cats FurReal"
CHANNEL_HASHTAGS = ["#Shorts", "#CatsFurReal", "#CuteCats", "#FunnyCats", "#Trending"]

# Upload Quality Settings
VIDEO_QUALITY = "hd720"  # Options: "hd720", "hd1080", "medium", "high"

# Retry Configuration
MAX_RETRIES = 3
RETRY_DELAY = 60  # seconds

# Time Zone Configuration (for scheduling)
TIMEZONE = "US/Eastern"  # Change to your preferred timezone

# Randomization Settings
MIN_DELAY_BETWEEN_UPLOADS = 300  # 5 minutes minimum between uploads
MAX_DELAY_BETWEEN_UPLOADS = 1800  # 30 minutes maximum between uploads

# Description Template
DESCRIPTION_TEMPLATE = """
{original_description}

🐱 Welcome to Cats FurReal – where paws, purrs, and pure chaos collide! 😹

🔔 Subscribe for daily cat content!
👍 Like if this made you smile!
💬 Tell us about your cat in the comments!
📤 Share with fellow cat lovers!

Does your cat do this too? Let us know! 👇

{channel_watermark}

{hashtags}
"""

# Title Enhancement - Optimized for Cat Content
TITLE_PREFIXES = [
    "😻", "🐱", "🔥", "❤️", "😂", "🥰", "✨", "🎉", "💕", "🌟", "😹", "🐾"
]

# Engagement Boosting Features
ENGAGEMENT_FEATURES = {
    "add_emoji_to_title": True,
    "enhance_description": True,
    "optimize_tags": True,
    "add_trending_hashtags": True,
    "randomize_upload_time": True,  # Add random delay to scheduled times
}

# Monitoring and Analytics
ENABLE_LOGGING = True
LOG_LEVEL = "INFO"  # Options: "DEBUG", "INFO", "WARNING", "ERROR"

# Safety Settings
SKIP_DUPLICATE_TITLES = True  # Skip videos with similar titles
TITLE_SIMILARITY_THRESHOLD = 0.8  # 80% similarity threshold

# Advanced Settings
ENABLE_THUMBNAIL_OPTIMIZATION = False  # Future feature
ENABLE_AUTO_CAPTIONS = False  # Future feature
ENABLE_COMMUNITY_POST = False  # Future feature

# Error Handling
CONTINUE_ON_ERROR = True  # Continue with next video if one fails
NOTIFY_ON_ERROR = False  # Future feature for email notifications

# Performance Settings
MAX_CONCURRENT_UPLOADS = 1  # Keep at 1 to avoid API limits
UPLOAD_CHUNK_SIZE = 1024 * 1024  # 1MB chunks for upload

# Development/Testing
DRY_RUN = False  # Set to True for testing without actual uploads
TEST_MODE = False  # Use test credentials and limited uploads
