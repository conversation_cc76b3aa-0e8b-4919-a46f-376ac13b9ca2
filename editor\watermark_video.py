import os
import json
import subprocess

DOWNLOADS_DIR = "downloads"
EDITOR_DIR = "editor"
CONVERTED_JSON = os.path.join(EDITOR_DIR, "converted.json")
LOGO_PATH = "logo.png"
WATERMARKED_OUTPUT_DIR = "watermarked_videos"

def ensure_dir(directory):
    if not os.path.exists(directory):
        os.makedirs(directory)

def load_converted_data():
    ensure_dir(EDITOR_DIR)
    if os.path.exists(CONVERTED_JSON):
        with open(CONVERTED_JSON, 'r') as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return []
    return []

def save_converted_data(data):
    ensure_dir(EDITOR_DIR)
    with open(CONVERTED_JSON, 'w') as f:
        json.dump(data, f, indent=4)

def get_all_video_files(directory):
    video_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                video_files.append(os.path.join(root, file))
    return video_files

def get_video_title(video_path):
    # Extract channel and video filename from the path
    parts = video_path.split(os.sep)
    if len(parts) >= 2:
        channel_name = parts[-2]
        video_filename_with_ext = parts[-1]
        video_filename = os.path.splitext(video_filename_with_ext)[0]
    else:
        return os.path.splitext(os.path.basename(video_path))[0] # Fallback to just filename

    metadata_path = os.path.join(DOWNLOADS_DIR, channel_name, "metadata.json")
    
    if os.path.exists(metadata_path):
        with open(metadata_path, 'r', encoding='utf-8') as f:
            try:
                metadata = json.load(f)
                for video_info in metadata.get('videos', []):
                    if video_info.get('filename') == video_filename_with_ext:
                        return video_info.get('title', video_filename)
            except json.JSONDecodeError:
                pass # Fallback to filename if JSON is malformed
    
    return video_filename # Fallback to filename if metadata.json not found or title not in it

def watermark_video(input_video_path, output_video_path, logo_path):
    # FFmpeg command to overlay logo at bottom-left, scaled to 6% of video width
    # and 10 pixels padding from left and bottom.
    filter_complex = "[0:v]eq=brightness=0.02:contrast=1.02:saturation=1.02[video_eq];[1:v]scale=iw*0.06:-1[logo];[video_eq][logo]overlay=10:H-h-10"

    command = [
        os.path.join("ffmpeg", "bin", "ffmpeg.exe"),
        "-i", input_video_path,
        "-i", logo_path,
        "-filter_complex", filter_complex,
        "-c:a", "copy",
        output_video_path
    ]
    try:
        subprocess.run(command, check=True, capture_output=True, text=True, encoding='utf-8')
        print(f"Successfully watermarked: {input_video_path} -> {output_video_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error watermarking {input_video_path}:")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False
    except FileNotFoundError:
        print("FFmpeg executable not found at 'ffmpeg/bin/ffmpeg.exe'. Please ensure the path is correct.")
        return False

def main():
    ensure_dir(WATERMARKED_OUTPUT_DIR)
    converted_data = load_converted_data()
    processed_videos = {item['filename']: item['edited'] for item in converted_data}

    all_videos = get_all_video_files(DOWNLOADS_DIR)
    
    for video_path in all_videos:
        relative_path = os.path.relpath(video_path, start=DOWNLOADS_DIR)
        
        if processed_videos.get(relative_path) == "yes":
            print(f"Skipping already processed video: {relative_path}")
            continue

        print(f"Processing video: {video_path}")
        
        # Construct output path
        output_video_path = os.path.join(WATERMARKED_OUTPUT_DIR, relative_path)
        
        # Ensure output directory structure exists
        ensure_dir(os.path.dirname(output_video_path))

        if watermark_video(video_path, output_video_path, LOGO_PATH):
            # Update converted_data
            found = False
            for item in converted_data:
                if item['filename'] == relative_path:
                    item['edited'] = "yes"
                    found = True
                    break
            if not found:
                converted_data.append({"filename": relative_path, "edited": "yes"})
            save_converted_data(converted_data)
            print(f"Updated {CONVERTED_JSON} for {relative_path}")
        else:
            print(f"Failed to watermark {video_path}. Status not updated in {CONVERTED_JSON}.")

if __name__ == "__main__":
    main()
