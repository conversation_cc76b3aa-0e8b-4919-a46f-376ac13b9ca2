"""
YouTube Uploader Module for YouTube Shorts Auto Uploader
Handles YouTube API authentication and video uploads
"""

import os
import json
import logging
import random
from datetime import datetime
from typing import Dict, Optional, List
import time

# Google API imports
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload

import config
from video_selector import VideoSelector

class YouTubeUploader:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.service = None
        self.video_selector = VideoSelector()
        
    def authenticate(self) -> bool:
        """Authenticate with YouTube API"""
        creds = None
        token_file = "token.json"
        
        # Load existing credentials
        if os.path.exists(token_file):
            creds = Credentials.from_authorized_user_file(token_file, config.YOUTUBE_API_SCOPES)
        
        # If no valid credentials, get new ones
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                except Exception as e:
                    self.logger.error(f"Error refreshing credentials: {e}")
                    creds = None
            
            if not creds:
                if not os.path.exists(config.CLIENT_SECRETS_FILE):
                    self.logger.error(f"Client secrets file not found: {config.CLIENT_SECRETS_FILE}")
                    return False
                
                flow = InstalledAppFlow.from_client_secrets_file(
                    config.CLIENT_SECRETS_FILE, config.YOUTUBE_API_SCOPES)
                creds = flow.run_local_server(port=0)
            
            # Save credentials for next run
            with open(token_file, 'w') as token:
                token.write(creds.to_json())
        
        try:
            self.service = build(config.YOUTUBE_API_SERVICE_NAME, config.YOUTUBE_API_VERSION, credentials=creds)
            self.logger.info("Successfully authenticated with YouTube API")
            return True
        except Exception as e:
            self.logger.error(f"Error building YouTube service: {e}")
            return False
    
    def enhance_title(self, original_title: str) -> str:
        """Enhance title with emojis and optimization"""
        if not config.ENGAGEMENT_FEATURES.get("add_emoji_to_title", False):
            return original_title
        
        # Add random emoji prefix
        emoji = random.choice(config.TITLE_PREFIXES)
        enhanced_title = f"{emoji} {original_title}"
        
        # Ensure title is not too long (YouTube limit is 100 characters)
        if len(enhanced_title) > 97:
            enhanced_title = enhanced_title[:97] + "..."
        
        return enhanced_title
    
    def enhance_description(self, video_data: Dict) -> str:
        """Enhance description with channel branding and engagement features"""
        original_description = video_data.get("description", "")
        
        if not config.ENGAGEMENT_FEATURES.get("enhance_description", False):
            return original_description
        
        # Prepare hashtags
        hashtags = config.CHANNEL_HASHTAGS.copy()
        if config.ENGAGEMENT_FEATURES.get("add_trending_hashtags", False):
            video_tags = video_data.get("tags", [])
            # Convert some tags to hashtags
            for tag in video_tags[:5]:  # Use first 5 tags
                hashtag = f"#{tag.replace(' ', '').replace('#', '')}"
                if hashtag not in hashtags:
                    hashtags.append(hashtag)
        
        hashtags_str = " ".join(hashtags)
        
        # Use description template
        enhanced_description = config.DESCRIPTION_TEMPLATE.format(
            original_description=original_description,
            channel_watermark=config.CHANNEL_WATERMARK,
            hashtags=hashtags_str
        )
        
        return enhanced_description
    
    def optimize_tags(self, video_data: Dict) -> List[str]:
        """Optimize tags for better discoverability"""
        original_tags = video_data.get("tags", [])
        
        if not config.ENGAGEMENT_FEATURES.get("optimize_tags", False):
            return original_tags[:config.DEFAULT_TAGS_LIMIT]
        
        # Start with original tags
        optimized_tags = original_tags.copy()
        
        # Add trending keywords
        trending_keywords = [
            "viral", "trending", "shorts", "funny", "cute", "amazing",
            "must watch", "epic", "hilarious", "adorable"
        ]
        
        # Add relevant trending keywords that aren't already in tags
        for keyword in trending_keywords:
            if keyword not in [tag.lower() for tag in optimized_tags]:
                optimized_tags.append(keyword)
                if len(optimized_tags) >= config.DEFAULT_TAGS_LIMIT:
                    break
        
        return optimized_tags[:config.DEFAULT_TAGS_LIMIT]
    
    def upload_video(self, video_data: Dict) -> Optional[Dict]:
        """Upload a video to YouTube"""
        if not self.service:
            self.logger.error("YouTube service not authenticated")
            return None
        
        if config.DRY_RUN:
            self.logger.info(f"DRY RUN: Would upload {video_data['filename']}")
            return {
                "youtube_id": "dry_run_id",
                "status": "dry_run",
                "upload_date": datetime.now().isoformat()
            }
        
        try:
            # Prepare video metadata
            title = self.enhance_title(video_data.get("title", video_data.get("filename", "Untitled")))
            description = self.enhance_description(video_data)
            tags = self.optimize_tags(video_data)
            
            # Video upload body
            body = {
                "snippet": {
                    "title": title,
                    "description": description,
                    "tags": tags,
                    "categoryId": config.DEFAULT_CATEGORY_ID
                },
                "status": {
                    "privacyStatus": config.DEFAULT_PRIVACY_STATUS
                }
            }
            
            # Create media upload object
            media = MediaFileUpload(
                video_data["file_path"],
                chunksize=config.UPLOAD_CHUNK_SIZE,
                resumable=True
            )
            
            self.logger.info(f"Starting upload: {title}")
            
            # Execute upload
            insert_request = self.service.videos().insert(
                part=",".join(body.keys()),
                body=body,
                media_body=media
            )
            
            response = self._resumable_upload(insert_request)
            
            if response:
                upload_result = {
                    "youtube_id": response["id"],
                    "status": "uploaded",
                    "upload_date": datetime.now().isoformat(),
                    "title": title,
                    "privacy_status": config.DEFAULT_PRIVACY_STATUS
                }
                
                self.logger.info(f"Successfully uploaded: {title} (ID: {response['id']})")
                return upload_result
            else:
                self.logger.error(f"Upload failed for: {title}")
                return None
                
        except HttpError as e:
            self.logger.error(f"HTTP error during upload: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error during upload: {e}")
            return None
    
    def _resumable_upload(self, insert_request):
        """Handle resumable upload with retry logic"""
        response = None
        error = None
        retry = 0
        
        while response is None:
            try:
                status, response = insert_request.next_chunk()
                if response is not None:
                    if 'id' in response:
                        return response
                    else:
                        raise Exception(f"Upload failed with response: {response}")
            except HttpError as e:
                if e.resp.status in [500, 502, 503, 504]:
                    error = f"Server error: {e}"
                    retry += 1
                    if retry > config.MAX_RETRIES:
                        raise Exception(f"Max retries exceeded: {error}")
                    
                    wait_time = config.RETRY_DELAY * (2 ** (retry - 1))
                    self.logger.warning(f"Retrying in {wait_time} seconds... (attempt {retry})")
                    time.sleep(wait_time)
                else:
                    raise e
            except Exception as e:
                error = f"Unexpected error: {e}"
                retry += 1
                if retry > config.MAX_RETRIES:
                    raise Exception(f"Max retries exceeded: {error}")
                
                wait_time = config.RETRY_DELAY
                self.logger.warning(f"Retrying in {wait_time} seconds... (attempt {retry})")
                time.sleep(wait_time)
        
        return response
    
    def upload_random_video(self) -> bool:
        """Select and upload a random video"""
        # Select random video
        video_data = self.video_selector.select_random_video()
        if not video_data:
            self.logger.warning("No videos available for upload")
            return False
        
        # Upload video
        upload_result = self.upload_video(video_data)
        if upload_result:
            # Mark as uploaded
            self.video_selector.mark_video_as_uploaded(video_data, upload_result)
            return True
        else:
            self.logger.error(f"Failed to upload video: {video_data['filename']}")
            return False


def main():
    """Test the uploader"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    uploader = YouTubeUploader()
    
    # Authenticate
    if not uploader.authenticate():
        print("Authentication failed!")
        return
    
    # Upload a random video
    success = uploader.upload_random_video()
    if success:
        print("Video uploaded successfully!")
    else:
        print("Upload failed!")


if __name__ == "__main__":
    main()
